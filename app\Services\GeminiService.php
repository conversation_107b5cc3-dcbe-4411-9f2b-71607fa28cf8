<?php

namespace App\Services;

use Exception;

class GeminiService
{
    private $apiKey;
    private $baseUrl;
    private $model;

    public function __construct()
    {
        $this->apiKey = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';
        $this->baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
        $this->model = 'gemini-2.5-pro-preview-05-06'; // Using the requested model
    }

    /**
     * Extract text from a file using Gemini AI
     * 
     * @param string $filePath Full path to the file
     * @param string $mimeType MIME type of the file
     * @return string|null Extracted text or null on failure
     */
    public function extractTextFromFile($filePath, $mimeType)
    {
        try {
            // Check if file exists
            if (!file_exists($filePath)) {
                log_message('error', "GeminiService: File not found: {$filePath}");
                return null;
            }

            // Get file size
            $fileSize = filesize($filePath);
            
            // For files under 20MB, use inline data
            if ($fileSize < 20 * 1024 * 1024) {
                return $this->extractTextInline($filePath, $mimeType);
            } else {
                // For larger files, use File API
                return $this->extractTextViaFileAPI($filePath, $mimeType);
            }
        } catch (Exception $e) {
            log_message('error', "GeminiService: Error extracting text: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract text using inline data (for files < 20MB)
     */
    private function extractTextInline($filePath, $mimeType)
    {
        try {
            // Read and encode file
            $fileContent = file_get_contents($filePath);
            $base64Content = base64_encode($fileContent);

            // Prepare the prompt based on file type
            $prompt = $this->getExtractionPrompt($mimeType);

            // Prepare request data
            $requestData = [
                'contents' => [
                    [
                        'parts' => [
                            [
                                'inline_data' => [
                                    'mime_type' => $mimeType,
                                    'data' => $base64Content
                                ]
                            ],
                            [
                                'text' => $prompt
                            ]
                        ]
                    ]
                ]
            ];

            // Make API request
            $response = $this->makeApiRequest($requestData);
            
            if ($response && isset($response['candidates'][0]['content']['parts'][0]['text'])) {
                return $response['candidates'][0]['content']['parts'][0]['text'];
            }

            return null;
        } catch (Exception $e) {
            log_message('error', "GeminiService: Error in inline extraction: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract text using File API (for files >= 20MB)
     */
    private function extractTextViaFileAPI($filePath, $mimeType)
    {
        try {
            // Upload file first
            $fileUri = $this->uploadFileToGemini($filePath, $mimeType);
            
            if (!$fileUri) {
                return null;
            }

            // Prepare the prompt
            $prompt = $this->getExtractionPrompt($mimeType);

            // Prepare request data
            $requestData = [
                'contents' => [
                    [
                        'parts' => [
                            [
                                'file_data' => [
                                    'mime_type' => $mimeType,
                                    'file_uri' => $fileUri
                                ]
                            ],
                            [
                                'text' => $prompt
                            ]
                        ]
                    ]
                ]
            ];

            // Make API request
            $response = $this->makeApiRequest($requestData);
            
            if ($response && isset($response['candidates'][0]['content']['parts'][0]['text'])) {
                return $response['candidates'][0]['content']['parts'][0]['text'];
            }

            return null;
        } catch (Exception $e) {
            log_message('error', "GeminiService: Error in File API extraction: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Upload file to Gemini File API
     */
    private function uploadFileToGemini($filePath, $mimeType)
    {
        try {
            $fileSize = filesize($filePath);
            $displayName = basename($filePath);

            // Step 1: Initialize resumable upload
            $initUrl = $this->baseUrl . "/upload/v1beta/files?key=" . $this->apiKey;
            
            $initHeaders = [
                'X-Goog-Upload-Protocol: resumable',
                'X-Goog-Upload-Command: start',
                'X-Goog-Upload-Header-Content-Length: ' . $fileSize,
                'X-Goog-Upload-Header-Content-Type: ' . $mimeType,
                'Content-Type: application/json'
            ];

            $initData = json_encode([
                'file' => [
                    'display_name' => $displayName
                ]
            ]);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $initUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $initData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $initHeaders);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HEADER, true);
            curl_setopt($ch, CURLOPT_NOBODY, false);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode !== 200) {
                log_message('error', "GeminiService: Failed to initialize upload. HTTP Code: {$httpCode}");
                return null;
            }

            // Extract upload URL from headers
            preg_match('/x-goog-upload-url:\s*(.+)/i', $response, $matches);
            if (!isset($matches[1])) {
                log_message('error', "GeminiService: Could not extract upload URL from response");
                return null;
            }

            $uploadUrl = trim($matches[1]);

            // Step 2: Upload file content
            $uploadHeaders = [
                'Content-Length: ' . $fileSize,
                'X-Goog-Upload-Offset: 0',
                'X-Goog-Upload-Command: upload, finalize'
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $uploadUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, file_get_contents($filePath));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $uploadHeaders);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            $uploadResponse = curl_exec($ch);
            $uploadHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($uploadHttpCode !== 200) {
                log_message('error', "GeminiService: Failed to upload file. HTTP Code: {$uploadHttpCode}");
                return null;
            }

            $uploadData = json_decode($uploadResponse, true);
            
            if (isset($uploadData['file']['uri'])) {
                return $uploadData['file']['uri'];
            }

            return null;
        } catch (Exception $e) {
            log_message('error', "GeminiService: Error uploading file: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Make API request to Gemini
     */
    private function makeApiRequest($requestData)
    {
        try {
            $url = $this->baseUrl . "/models/{$this->model}:generateContent?key=" . $this->apiKey;

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json'
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 120); // 2 minutes timeout

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode !== 200) {
                log_message('error', "GeminiService: API request failed. HTTP Code: {$httpCode}, Response: {$response}");
                return null;
            }

            return json_decode($response, true);
        } catch (Exception $e) {
            log_message('error', "GeminiService: Error making API request: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get appropriate extraction prompt based on file type
     */
    private function getExtractionPrompt($mimeType)
    {
        switch ($mimeType) {
            case 'application/pdf':
                return 'Please extract all text content from this PDF document. Include all readable text, preserving the structure and formatting as much as possible. If there are tables, charts, or diagrams, describe their content as well.';
            
            case 'application/msword':
            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                return 'Please extract all text content from this document. Preserve the structure, headings, and formatting. Include any text from tables or embedded content.';
            
            case 'image/jpeg':
            case 'image/jpg':
            case 'image/png':
            case 'image/webp':
                return 'Please extract all visible text from this image. This includes any text in documents, signs, labels, or any other readable content. Preserve the text structure and formatting as much as possible.';
            
            default:
                return 'Please extract all text content from this file. Include all readable text and describe any visual elements that contain information.';
        }
    }

    /**
     * Check if file type is supported for text extraction
     */
    public function isSupportedFileType($mimeType)
    {
        $supportedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'image/webp',
            'text/plain'
        ];

        return in_array($mimeType, $supportedTypes);
    }
}
